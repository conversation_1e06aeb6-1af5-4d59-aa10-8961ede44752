@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 210 15% 20%;

    --card: 0 0% 100%;
    --card-foreground: 210 15% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 15% 20%;

    /* Agro primary: Professional green */
    --primary: 152 60% 35%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 152 50% 45%;
    --primary-dark: 152 70% 25%;

    /* Agro secondary: Tech blue */
    --secondary: 210 40% 96%;
    --secondary-foreground: 210 15% 20%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 152 30% 95%;
    --accent-foreground: 152 60% 35%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 152 60% 35%;

    /* Custom agro colors */
    --agro-blue: 210 100% 50%;
    --agro-blue-light: 210 100% 96%;
    --agro-green-light: 152 40% 92%;
    --agro-success: 120 60% 50%;
    --agro-warning: 45 100% 60%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(152 60% 35%), hsl(152 70% 25%));
    --gradient-hero: linear-gradient(135deg, hsl(152 60% 35%), hsl(210 100% 50%));
    --gradient-card: linear-gradient(180deg, hsl(0 0% 100%), hsl(152 30% 98%));
    
    /* Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(152 60% 35% / 0.2);
    --shadow-card: 0 4px 6px -1px hsl(0 0% 0% / 0.1);
    
    /* Animation */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}